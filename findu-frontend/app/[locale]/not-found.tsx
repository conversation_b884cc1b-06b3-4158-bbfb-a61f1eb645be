import Link from 'next/link';
import { useTranslation } from '@/lib/i18n';

export default function NotFound({ params }: { params: { locale: string } }) {
  const { t, locale } = useTranslation(params.locale);

  return (
    <div className="container mx-auto p-10 text-center">
      <h1 className="text-4xl font-bold mb-4">{t('not_found')}</h1>
      <p className="text-gray-600 mb-6">{t('not_found_message')}</p>
      <Link href={`/${locale}`} className="text-blue-500 underline">
        {t('back_to_home')}
      </Link>
    </div>
  );
}