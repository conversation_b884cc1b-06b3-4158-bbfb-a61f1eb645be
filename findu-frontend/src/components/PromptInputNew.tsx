/**
 * 新版提示词输入组件 - 使用内联样式
 */

'use client';

import { useState } from 'react';

interface PromptInputProps {
  onSubmit: (prompt: string) => void;
  disabled?: boolean;
  placeholder?: string;
}

export default function PromptInputNew({
  onSubmit,
  disabled = false,
  placeholder = "例如：我想开发一个在线教育平台，包含课程管理、学生管理、在线考试等功能..."
}: PromptInputProps) {
  const [prompt, setPrompt] = useState('');

  const handleSubmit = () => {
    if (prompt.trim() && !disabled) {
      onSubmit(prompt.trim());
    }
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLTextAreaElement>) => {
    setPrompt(event.target.value);
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (event.key === 'Enter' && (event.ctrlKey || event.metaKey)) {
      event.preventDefault();
      handleSubmit();
    }
  };

  return (
    <div style={{ width: '100%' }}>
      <div style={{ position: 'relative' }}>
        {/* 文本域 */}
        <textarea
          value={prompt}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          disabled={disabled}
          maxLength={2000}
          rows={6}
          style={{
            width: '100%',
            padding: '1.25rem',
            fontSize: '1rem',
            lineHeight: '1.6',
            border: '2px solid #e5e7eb',
            borderRadius: '16px',
            outline: 'none',
            transition: 'all 0.2s ease',
            resize: 'vertical',
            minHeight: '140px',
            fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
            backgroundColor: disabled ? '#f9fafb' : 'white',
            color: disabled ? '#9ca3af' : '#1f2937'
          }}
          onFocus={(e) => {
            if (!disabled) {
              e.target.style.borderColor = '#3b82f6';
              e.target.style.boxShadow = '0 0 0 4px rgba(59, 130, 246, 0.1)';
            }
          }}
          onBlur={(e) => {
            e.target.style.borderColor = '#e5e7eb';
            e.target.style.boxShadow = 'none';
          }}
        />

        {/* 字符计数 */}
        <div style={{
          position: 'absolute',
          bottom: '0.75rem',
          right: '1rem',
          fontSize: '0.75rem',
          color: '#9ca3af',
          background: 'rgba(255, 255, 255, 0.9)',
          padding: '0.25rem 0.5rem',
          borderRadius: '6px'
        }}>
          {prompt.length}/2000
        </div>
      </div>

      {/* 提交按钮 */}
      <div style={{ textAlign: 'center', marginTop: '1.5rem' }}>
        <button
          onClick={handleSubmit}
          disabled={disabled || !prompt.trim()}
          style={{
            display: 'inline-flex',
            alignItems: 'center',
            gap: '0.75rem',
            padding: '1rem 2.5rem',
            background: disabled || !prompt.trim() 
              ? '#d1d5db' 
              : 'linear-gradient(135deg, #3b82f6, #6366f1)',
            color: 'white',
            border: 'none',
            borderRadius: '16px',
            fontSize: '1.125rem',
            fontWeight: '600',
            cursor: disabled || !prompt.trim() ? 'not-allowed' : 'pointer',
            boxShadow: disabled || !prompt.trim() 
              ? 'none' 
              : '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)',
            transition: 'all 0.2s ease',
            fontFamily: 'inherit'
          }}
          onMouseOver={(e) => {
            if (!disabled && prompt.trim()) {
              e.currentTarget.style.transform = 'translateY(-2px)';
              e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)';
            }
          }}
          onMouseOut={(e) => {
            if (!disabled && prompt.trim()) {
              e.currentTarget.style.transform = 'translateY(0)';
              e.currentTarget.style.boxShadow = '0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)';
            }
          }}
        >
          <svg style={{ width: '24px', height: '24px' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
          {disabled ? '生成中...' : '生成案例'}
        </button>
      </div>

      {/* 使用提示 */}
      <div style={{ textAlign: 'center', marginTop: '1rem' }}>
        <p style={{
          fontSize: '0.875rem',
          color: '#9ca3af',
          margin: 0
        }}>
          💡 提示：描述越详细，生成的案例越精准 | 支持 Ctrl+Enter 快速提交
        </p>
      </div>

      {/* 功能提示 */}
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        gap: '2rem',
        marginTop: '1rem',
        flexWrap: 'wrap'
      }}>
        {[
          { icon: '✅', text: '支持中英文', color: '#10b981' },
          { icon: '🎯', text: 'AI智能分析', color: '#3b82f6' },
          { icon: '⚡', text: '快速生成', color: '#8b5cf6' }
        ].map((item, index) => (
          <div key={index} style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.5rem',
            fontSize: '0.875rem',
            color: '#6b7280'
          }}>
            <span style={{ color: item.color }}>{item.icon}</span>
            <span>{item.text}</span>
          </div>
        ))}
      </div>
    </div>
  );
}
