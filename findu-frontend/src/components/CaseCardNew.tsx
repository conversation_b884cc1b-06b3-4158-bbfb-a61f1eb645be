/**
 * 新版案例卡片组件 - 使用内联样式
 */

'use client';

import { Case } from '@/lib/types';

interface CaseCardProps {
  case: Case;
  isSelected: boolean;
  onSelect: () => void;
}

export default function CaseCardNew({ case: caseItem, isSelected, onSelect }: CaseCardProps) {
  return (
    <div
      onClick={onSelect}
      style={{
        background: isSelected 
          ? 'linear-gradient(135deg, #dbeafe, #e0e7ff)' 
          : 'white',
        border: isSelected 
          ? '2px solid #3b82f6' 
          : '1px solid #e5e7eb',
        borderRadius: '16px',
        padding: '1.5rem',
        cursor: 'pointer',
        transition: 'all 0.2s ease',
        boxShadow: isSelected 
          ? '0 10px 15px -3px rgba(59, 130, 246, 0.1), 0 4px 6px -2px rgba(59, 130, 246, 0.05)' 
          : '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        position: 'relative',
        overflow: 'hidden'
      }}
      onMouseOver={(e) => {
        if (!isSelected) {
          e.currentTarget.style.transform = 'translateY(-4px)';
          e.currentTarget.style.boxShadow = '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)';
        }
      }}
      onMouseOut={(e) => {
        if (!isSelected) {
          e.currentTarget.style.transform = 'translateY(0)';
          e.currentTarget.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)';
        }
      }}
    >
      {/* 选中状态指示器 */}
      {isSelected && (
        <div style={{
          position: 'absolute',
          top: '1rem',
          right: '1rem',
          width: '24px',
          height: '24px',
          background: '#3b82f6',
          borderRadius: '50%',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center'
        }}>
          <svg style={{ width: '14px', height: '14px', color: 'white' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
          </svg>
        </div>
      )}

      {/* 案例图标 */}
      <div style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        width: '48px',
        height: '48px',
        background: isSelected 
          ? 'linear-gradient(135deg, #3b82f6, #6366f1)' 
          : 'linear-gradient(135deg, #f3f4f6, #e5e7eb)',
        borderRadius: '12px',
        marginBottom: '1rem'
      }}>
        <svg style={{ 
          width: '24px', 
          height: '24px', 
          color: isSelected ? 'white' : '#6b7280' 
        }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
      </div>

      {/* 案例标题 */}
      <h3 style={{
        fontSize: '1.25rem',
        fontWeight: '600',
        color: '#1f2937',
        marginBottom: '0.75rem',
        lineHeight: '1.3'
      }}>
        {caseItem.title}
      </h3>

      {/* 案例描述 */}
      <p style={{
        fontSize: '0.875rem',
        color: '#6b7280',
        lineHeight: '1.5',
        marginBottom: '1.5rem'
      }}>
        {caseItem.description}
      </p>

      {/* 功能列表 */}
      {caseItem.features && caseItem.features.length > 0 && (
        <div style={{ marginBottom: '1.5rem' }}>
          <h4 style={{
            fontSize: '0.875rem',
            fontWeight: '500',
            color: '#374151',
            marginBottom: '0.75rem'
          }}>
            🔧 主要功能：
          </h4>
          <div style={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: '0.5rem'
          }}>
            {caseItem.features.slice(0, 4).map((feature, index) => (
              <span
                key={index}
                style={{
                  display: 'inline-block',
                  padding: '0.25rem 0.75rem',
                  background: isSelected 
                    ? 'rgba(59, 130, 246, 0.1)' 
                    : '#f3f4f6',
                  color: isSelected ? '#3b82f6' : '#6b7280',
                  borderRadius: '12px',
                  fontSize: '0.75rem',
                  fontWeight: '500'
                }}
              >
                {feature}
              </span>
            ))}
            {caseItem.features.length > 4 && (
              <span style={{
                display: 'inline-block',
                padding: '0.25rem 0.75rem',
                background: '#f3f4f6',
                color: '#9ca3af',
                borderRadius: '12px',
                fontSize: '0.75rem'
              }}>
                +{caseItem.features.length - 4}
              </span>
            )}
          </div>
        </div>
      )}

      {/* 技术栈 */}
      {caseItem.technologies && caseItem.technologies.length > 0 && (
        <div style={{ marginBottom: '1rem' }}>
          <h4 style={{
            fontSize: '0.875rem',
            fontWeight: '500',
            color: '#374151',
            marginBottom: '0.75rem'
          }}>
            💻 技术栈：
          </h4>
          <div style={{
            display: 'flex',
            flexWrap: 'wrap',
            gap: '0.5rem'
          }}>
            {caseItem.technologies.slice(0, 3).map((tech, index) => (
              <span
                key={index}
                style={{
                  display: 'inline-block',
                  padding: '0.25rem 0.75rem',
                  background: isSelected 
                    ? 'rgba(99, 102, 241, 0.1)' 
                    : '#f8fafc',
                  color: isSelected ? '#6366f1' : '#64748b',
                  borderRadius: '12px',
                  fontSize: '0.75rem',
                  fontWeight: '500',
                  border: '1px solid',
                  borderColor: isSelected ? 'rgba(99, 102, 241, 0.2)' : '#e2e8f0'
                }}
              >
                {tech}
              </span>
            ))}
            {caseItem.technologies.length > 3 && (
              <span style={{
                display: 'inline-block',
                padding: '0.25rem 0.75rem',
                background: '#f8fafc',
                color: '#94a3b8',
                borderRadius: '12px',
                fontSize: '0.75rem',
                border: '1px solid #e2e8f0'
              }}>
                +{caseItem.technologies.length - 3}
              </span>
            )}
          </div>
        </div>
      )}

      {/* 选择提示 */}
      <div style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        paddingTop: '1rem',
        borderTop: '1px solid',
        borderColor: isSelected ? 'rgba(59, 130, 246, 0.2)' : '#f3f4f6'
      }}>
        <span style={{
          fontSize: '0.875rem',
          color: isSelected ? '#3b82f6' : '#9ca3af',
          fontWeight: '500'
        }}>
          {isSelected ? '✅ 已选择' : '点击选择'}
        </span>
        
        {/* 复杂度指示器 */}
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '0.25rem'
        }}>
          <span style={{
            fontSize: '0.75rem',
            color: '#9ca3af'
          }}>
            复杂度:
          </span>
          {[1, 2, 3, 4, 5].map((level) => (
            <div
              key={level}
              style={{
                width: '6px',
                height: '6px',
                borderRadius: '50%',
                background: level <= (caseItem.complexity || 3) 
                  ? (isSelected ? '#3b82f6' : '#6b7280') 
                  : '#e5e7eb'
              }}
            />
          ))}
        </div>
      </div>
    </div>
  );
}
