/**
 * 新版首页客户端组件 - 使用内联样式确保样式正确显示
 */

'use client';

import { useState } from 'react';
import PromptInputNew from '@/components/PromptInputNew';
import CaseCardNew from '@/components/CaseCardNew';
import DocumentPreview from '@/components/DocumentPreview';
import { generateCases, generateDemand } from '@/lib/api';
import { Case } from '@/lib/types';
import { useTranslation } from '@/lib/i18n';

interface HomeClientProps {
  locale: string;
}

export default function HomeClientNew({ locale }: HomeClientProps) {
  const { t } = useTranslation(locale);
  const [prompt, setPrompt] = useState('');
  const [cases, setCases] = useState<Case[]>([]);
  const [selectedCases, setSelectedCases] = useState<Case[]>([]);
  const [loading, setLoading] = useState(false);
  const [documentUrl, setDocumentUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);

  // 处理案例生成
  const handleGenerateCases = async (inputPrompt: string) => {
    if (!inputPrompt.trim()) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const generatedCases = await generateCases(inputPrompt);
      setCases(generatedCases);
    } catch (err) {
      setError('生成案例时出错，请重试');
      console.error('Error generating cases:', err);
    } finally {
      setLoading(false);
    }
  };

  // 处理案例选择
  const handleCaseSelect = (caseItem: Case) => {
    setSelectedCases(prev => {
      const isSelected = prev.some(c => c.id === caseItem.id);
      if (isSelected) {
        return prev.filter(c => c.id !== caseItem.id);
      } else {
        return [...prev, caseItem];
      }
    });
  };

  // 处理文档生成
  const handleGenerateDocument = async () => {
    if (selectedCases.length === 0) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const url = await generateDemand(prompt, selectedCases);
      setDocumentUrl(url);
    } catch (err) {
      setError('生成文档时出错，请重试');
      console.error('Error generating document:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div style={{
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #f0f9ff 0%, #ffffff 50%, #f0f4ff 100%)',
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
      padding: '2rem 1rem'
    }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
        
        {/* 英雄区域 */}
        <div style={{ textAlign: 'center', marginBottom: '4rem' }}>
          {/* 主图标 */}
          <div style={{
            display: 'inline-flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: '80px',
            height: '80px',
            background: 'linear-gradient(135deg, #3b82f6, #6366f1)',
            borderRadius: '20px',
            marginBottom: '2rem',
            boxShadow: '0 10px 25px rgba(59, 130, 246, 0.3)'
          }}>
            <svg style={{ width: '40px', height: '40px', color: 'white' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
          
          {/* 主标题 */}
          <h1 style={{
            fontSize: '3rem',
            fontWeight: 'bold',
            background: 'linear-gradient(135deg, #3b82f6, #6366f1)',
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            backgroundClip: 'text',
            marginBottom: '1.5rem',
            lineHeight: '1.2'
          }}>
            {t('title')}
          </h1>
          
          {/* 副标题 */}
          <p style={{
            fontSize: '1.25rem',
            color: '#6b7280',
            marginBottom: '3rem',
            maxWidth: '600px',
            margin: '0 auto 3rem auto',
            lineHeight: '1.6'
          }}>
            {t('subtitle')}
          </p>
          
          {/* 状态指示器 */}
          <div style={{ 
            display: 'flex', 
            flexWrap: 'wrap', 
            justifyContent: 'center', 
            gap: '1.5rem', 
            marginBottom: '3rem' 
          }}>
            {[
              { color: '#10b981', text: 'AI驱动' },
              { color: '#3b82f6', text: '智能分析' },
              { color: '#8b5cf6', text: '快速生成' }
            ].map((item, index) => (
              <div key={index} style={{
                display: 'flex',
                alignItems: 'center',
                gap: '0.5rem',
                background: 'rgba(255, 255, 255, 0.9)',
                padding: '0.75rem 1.5rem',
                borderRadius: '25px',
                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                border: '1px solid rgba(255, 255, 255, 0.2)'
              }}>
                <div style={{
                  width: '8px',
                  height: '8px',
                  backgroundColor: item.color,
                  borderRadius: '50%'
                }}></div>
                <span style={{ 
                  fontSize: '0.875rem', 
                  fontWeight: '500', 
                  color: '#374151' 
                }}>
                  {item.text}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* 主要内容区域 */}
        <div style={{ maxWidth: '800px', margin: '0 auto' }}>
          
          {/* 输入区域 */}
          <div style={{
            background: 'rgba(255, 255, 255, 0.9)',
            borderRadius: '20px',
            padding: '2.5rem',
            marginBottom: '3rem',
            boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)',
            border: '1px solid rgba(255, 255, 255, 0.2)'
          }}>
            <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
              <h2 style={{
                fontSize: '1.5rem',
                fontWeight: '600',
                color: '#1f2937',
                marginBottom: '0.75rem'
              }}>
                💡 描述您的项目需求
              </h2>
              <p style={{
                color: '#6b7280',
                fontSize: '1rem'
              }}>
                简单描述您想要开发的项目，AI将为您生成详细的案例方案
              </p>
            </div>
            
            <PromptInputNew
              onSubmit={handleGenerateCases}
              disabled={loading}
              placeholder="例如：我想开发一个在线教育平台，包含课程管理、学生管理、在线考试等功能..."
            />
          </div>

          {/* 加载状态 */}
          {loading && (
            <div style={{
              background: 'linear-gradient(135deg, #dbeafe, #e0e7ff)',
              borderRadius: '20px',
              padding: '3rem',
              textAlign: 'center',
              marginBottom: '3rem',
              border: '1px solid #bfdbfe'
            }}>
              <div style={{
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '64px',
                height: '64px',
                background: 'linear-gradient(135deg, #3b82f6, #6366f1)',
                borderRadius: '16px',
                marginBottom: '1.5rem'
              }}>
                <div style={{
                  width: '32px',
                  height: '32px',
                  border: '4px solid white',
                  borderTop: '4px solid transparent',
                  borderRadius: '50%',
                  animation: 'spin 1s linear infinite'
                }}></div>
              </div>
              <h3 style={{
                fontSize: '1.25rem',
                fontWeight: '600',
                color: '#1f2937',
                marginBottom: '0.5rem'
              }}>
                AI正在分析您的需求
              </h3>
              <p style={{ color: '#6b7280' }}>
                正在生成个性化的项目案例，请稍候...
              </p>
            </div>
          )}

          {/* 错误提示 */}
          {error && (
            <div style={{
              background: '#fef2f2',
              border: '1px solid #fecaca',
              borderRadius: '12px',
              padding: '1rem',
              marginBottom: '2rem',
              color: '#dc2626'
            }}>
              {error}
            </div>
          )}

          {/* 案例展示区域 */}
          {cases.length > 0 && (
            <div style={{ marginBottom: '3rem' }}>
              <h3 style={{
                fontSize: '1.5rem',
                fontWeight: '600',
                color: '#1f2937',
                marginBottom: '1.5rem',
                textAlign: 'center'
              }}>
                🎯 为您推荐的项目案例
              </h3>
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
                gap: '1.5rem'
              }}>
                {cases.map((caseItem) => (
                  <CaseCardNew
                    key={caseItem.id}
                    case={caseItem}
                    isSelected={selectedCases.some(c => c.id === caseItem.id)}
                    onSelect={() => handleCaseSelect(caseItem)}
                  />
                ))}
              </div>
            </div>
          )}

          {/* 生成文档按钮 */}
          {selectedCases.length > 0 && (
            <div style={{
              background: 'linear-gradient(135deg, #ecfdf5, #d1fae5)',
              borderRadius: '20px',
              padding: '2.5rem',
              textAlign: 'center',
              marginBottom: '3rem',
              border: '1px solid #a7f3d0'
            }}>
              <div style={{
                display: 'inline-flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: '64px',
                height: '64px',
                background: 'linear-gradient(135deg, #10b981, #059669)',
                borderRadius: '16px',
                marginBottom: '1.5rem'
              }}>
                <svg style={{ width: '32px', height: '32px', color: 'white' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 style={{
                fontSize: '1.5rem',
                fontWeight: '600',
                color: '#1f2937',
                marginBottom: '1rem'
              }}>
                📋 生成需求文档
              </h3>
              <p style={{
                color: '#6b7280',
                marginBottom: '2rem'
              }}>
                已选择 {selectedCases.length} 个案例，点击生成详细的项目需求文档
              </p>
              <button
                onClick={handleGenerateDocument}
                disabled={loading}
                style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  gap: '0.5rem',
                  padding: '0.75rem 2rem',
                  background: 'linear-gradient(135deg, #3b82f6, #6366f1)',
                  color: 'white',
                  border: 'none',
                  borderRadius: '12px',
                  fontSize: '1rem',
                  fontWeight: '500',
                  cursor: 'pointer',
                  boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)',
                  transition: 'all 0.2s'
                }}
                onMouseOver={(e) => {
                  e.currentTarget.style.transform = 'translateY(-2px)';
                  e.currentTarget.style.boxShadow = '0 8px 15px rgba(0, 0, 0, 0.2)';
                }}
                onMouseOut={(e) => {
                  e.currentTarget.style.transform = 'translateY(0)';
                  e.currentTarget.style.boxShadow = '0 4px 6px rgba(0, 0, 0, 0.1)';
                }}
              >
                <svg style={{ width: '20px', height: '20px' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                {t('generate_document')}
              </button>
            </div>
          )}

          {/* 文档预览区域 */}
          {documentUrl && (
            <div style={{
              background: 'linear-gradient(135deg, #f0fdf4, #dcfce7)',
              borderRadius: '20px',
              padding: '3rem',
              border: '1px solid #bbf7d0'
            }}>
              <div style={{ textAlign: 'center', marginBottom: '2rem' }}>
                <div style={{
                  display: 'inline-flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  width: '80px',
                  height: '80px',
                  background: 'linear-gradient(135deg, #10b981, #059669)',
                  borderRadius: '20px',
                  marginBottom: '1.5rem'
                }}>
                  <svg style={{ width: '40px', height: '40px', color: 'white' }} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 style={{
                  fontSize: '2rem',
                  fontWeight: 'bold',
                  color: '#1f2937',
                  marginBottom: '1rem'
                }}>
                  🎉 需求文档生成完成！
                </h3>
                <p style={{
                  fontSize: '1.125rem',
                  color: '#6b7280',
                  marginBottom: '2rem'
                }}>
                  您的项目需求文档已经准备就绪，可以预览和下载
                </p>
              </div>
              <DocumentPreview documentUrl={documentUrl} locale={locale} />
            </div>
          )}
        </div>
      </div>

      {/* 添加旋转动画的CSS */}
      <style jsx>{`
        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
      `}</style>
    </div>
  );
}
