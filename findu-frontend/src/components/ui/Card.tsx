/**
 * 卡片组件库
 * 
 * 提供统一的卡片容器样式，用于组织和展示内容。
 * 遵循现代化设计原则，具有良好的视觉层次和交互效果。
 * 
 * 功能特性：
 * - 多种卡片变体（default, elevated, outlined）
 * - 可选的悬停效果
 * - 可点击状态支持
 * - 灵活的内容布局
 * - 响应式设计
 */

import React from 'react';
import { cn } from '@/lib/utils';

/**
 * 卡片组件属性接口
 */
interface CardProps extends React.HTMLAttributes<HTMLDivElement> {
  /** 卡片变体样式 */
  variant?: 'default' | 'elevated' | 'outlined';
  /** 是否可点击 */
  clickable?: boolean;
  /** 是否显示悬停效果 */
  hover?: boolean;
  /** 卡片内容 */
  children: React.ReactNode;
}

/**
 * 卡片头部组件属性接口
 */
interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

/**
 * 卡片内容组件属性接口
 */
interface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

/**
 * 卡片底部组件属性接口
 */
interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode;
}

/**
 * 卡片变体样式映射
 */
const cardVariants = {
  default: 'bg-white border border-gray-200',
  elevated: 'bg-white shadow-lg border border-gray-100',
  outlined: 'bg-white border-2 border-gray-300',
};

/**
 * 卡片组件
 * 
 * 提供一致的卡片容器样式。支持多种变体和交互状态，
 * 可以用于展示各种类型的内容。
 * 
 * @param props - 卡片属性
 * @returns React 函数组件
 */
export const Card: React.FC<CardProps> = ({
  variant = 'default',
  clickable = false,
  hover = false,
  className,
  children,
  ...props
}) => {
  return (
    <div
      className={cn(
        // 基础样式
        'rounded-lg transition-all duration-200',
        // 变体样式
        cardVariants[variant],
        // 交互样式
        clickable && 'cursor-pointer',
        hover && 'hover:shadow-lg hover:-translate-y-1',
        clickable && 'hover:shadow-lg hover:-translate-y-1',
        // 自定义样式
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

/**
 * 卡片头部组件
 * 
 * 用于显示卡片的标题和操作按钮等头部内容。
 * 
 * @param props - 卡片头部属性
 * @returns React 函数组件
 */
export const CardHeader: React.FC<CardHeaderProps> = ({
  className,
  children,
  ...props
}) => {
  return (
    <div
      className={cn(
        'px-6 py-4 border-b border-gray-200',
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

/**
 * 卡片内容组件
 * 
 * 用于显示卡片的主要内容区域。
 * 
 * @param props - 卡片内容属性
 * @returns React 函数组件
 */
export const CardContent: React.FC<CardContentProps> = ({
  className,
  children,
  ...props
}) => {
  return (
    <div
      className={cn(
        'px-6 py-4',
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

/**
 * 卡片底部组件
 * 
 * 用于显示卡片的操作按钮和其他底部内容。
 * 
 * @param props - 卡片底部属性
 * @returns React 函数组件
 */
export const CardFooter: React.FC<CardFooterProps> = ({
  className,
  children,
  ...props
}) => {
  return (
    <div
      className={cn(
        'px-6 py-4 border-t border-gray-200 bg-gray-50 rounded-b-lg',
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

export default Card;
