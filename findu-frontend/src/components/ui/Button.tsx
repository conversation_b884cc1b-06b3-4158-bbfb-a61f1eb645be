/**
 * 按钮组件库
 * 
 * 提供统一的按钮样式和交互效果，支持多种变体和尺寸。
 * 遵循现代化设计原则，具有良好的可访问性和用户体验。
 * 
 * 功能特性：
 * - 多种按钮变体（primary, secondary, outline, ghost）
 * - 多种尺寸（sm, md, lg）
 * - 加载状态支持
 * - 禁用状态支持
 * - 完整的键盘导航支持
 * - 响应式设计
 */

import React from 'react';
import { cn } from '@/lib/utils';

/**
 * 按钮组件属性接口
 */
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  /** 按钮变体样式 */
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
  /** 按钮尺寸 */
  size?: 'sm' | 'md' | 'lg';
  /** 是否显示加载状态 */
  loading?: boolean;
  /** 按钮内容 */
  children: React.ReactNode;
}

/**
 * 按钮变体样式映射
 */
const buttonVariants = {
  primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 shadow-lg hover:shadow-xl',
  secondary: 'bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500 shadow-lg hover:shadow-xl',
  outline: 'border-2 border-blue-600 text-blue-600 hover:bg-blue-50 focus:ring-blue-500 bg-white',
  ghost: 'text-gray-700 hover:bg-gray-100 focus:ring-gray-500',
  danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500 shadow-lg hover:shadow-xl',
};

/**
 * 按钮尺寸样式映射
 */
const buttonSizes = {
  sm: 'px-3 py-1.5 text-sm',
  md: 'px-4 py-2 text-base',
  lg: 'px-6 py-3 text-lg',
};

/**
 * 加载动画组件
 */
const LoadingSpinner = () => (
  <svg
    className="animate-spin -ml-1 mr-2 h-4 w-4"
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
  >
    <circle
      className="opacity-25"
      cx="12"
      cy="12"
      r="10"
      stroke="currentColor"
      strokeWidth="4"
    />
    <path
      className="opacity-75"
      fill="currentColor"
      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
    />
  </svg>
);

/**
 * 按钮组件
 * 
 * 提供一致的按钮样式和交互效果。支持多种变体、尺寸和状态，
 * 具有良好的可访问性和用户体验。
 * 
 * @param props - 按钮属性
 * @returns React 函数组件
 */
export const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled,
  className,
  children,
  ...props
}) => {
  return (
    <button
      className={cn(
        // 基础样式
        'inline-flex items-center justify-center rounded-lg font-medium transition-all duration-200',
        'focus:outline-none focus:ring-2 focus:ring-offset-2',
        'disabled:opacity-50 disabled:cursor-not-allowed disabled:shadow-none',
        // 变体样式
        buttonVariants[variant],
        // 尺寸样式
        buttonSizes[size],
        // 自定义样式
        className
      )}
      disabled={disabled || loading}
      {...props}
    >
      {loading && <LoadingSpinner />}
      {children}
    </button>
  );
};

export default Button;
