/**
 * 案例卡片组件
 *
 * 该组件用于展示单个需求案例的详细信息，包括标题、描述和详细要求。
 * 支持选中状态的视觉反馈，用户可以点击选择特定的案例。
 *
 * 功能特性：
 * - 案例信息展示（标题、描述、详细要求列表）
 * - 选中状态的视觉反馈
 * - 国际化支持
 * - 响应式设计
 * - 交互式选择功能
 */

import { Case } from '@/lib/types';
import { useTranslation } from '@/lib/i18n';
import { Card, CardContent, CardFooter } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

/**
 * CaseCard 组件的属性接口
 */
interface CaseCardProps {
  /** 要展示的案例数据对象 */
  caseItem: Case;
  /** 当前案例是否被选中 */
  isSelected: boolean;
  /** 用户选择该案例时的回调函数 */
  onSelect: () => void;
  /** 当前语言环境 */
  locale: string;
}

/**
 * 案例卡片组件
 *
 * 以卡片形式展示单个需求案例的完整信息，包括标题、描述和详细要求列表。
 * 当案例被选中时，会显示不同的视觉样式以提供用户反馈。
 *
 * @param props - 组件属性
 * @returns React 函数组件
 */
export default function CaseCard({ caseItem, isSelected, onSelect, locale }: CaseCardProps) {
  // 获取国际化翻译函数
  const { t } = useTranslation(locale);

  return (
    <Card
      variant={isSelected ? "elevated" : "default"}
      clickable={true}
      hover={!isSelected}
      className={`transition-all duration-300 transform hover:scale-105 ${
        isSelected
          ? 'ring-2 ring-blue-500 ring-offset-2 bg-gradient-to-br from-blue-50 to-indigo-50 shadow-xl'
          : 'hover:shadow-xl bg-white'
      }`}
      onClick={onSelect}
    >
      <CardContent className="p-6">
        {/* 案例标题 */}
        <h3 className="text-xl font-bold text-gray-900 mb-3 leading-tight">
          {caseItem.title}
        </h3>

        {/* 案例描述 */}
        <p className="text-gray-600 mb-4 leading-relaxed">
          {caseItem.description}
        </p>

        {/* 详细要求列表 */}
        <div className="space-y-2">
          <h4 className="text-sm font-semibold text-gray-700 mb-2">详细要求：</h4>
          <ul className="space-y-1">
            {caseItem.details.map((detail, idx) => (
              <li key={idx} className="flex items-start text-sm text-gray-600">
                <span className="inline-block w-1.5 h-1.5 bg-blue-500 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                <span>{detail}</span>
              </li>
            ))}
          </ul>
        </div>
      </CardContent>

      <CardFooter className="px-6 py-4">
        <Button
          variant={isSelected ? "primary" : "outline"}
          size="md"
          className="w-full"
          onClick={(e) => {
            e.stopPropagation();
            onSelect();
          }}
        >
          {isSelected ? '✓ 已选择' : t('select_case')}
        </Button>
      </CardFooter>
    </Card>
  );
}