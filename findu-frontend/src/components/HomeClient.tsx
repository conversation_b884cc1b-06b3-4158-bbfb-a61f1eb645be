/**
 * 主页面客户端组件
 * 
 * 该组件包含所有需要客户端交互的逻辑，包括状态管理、
 * 用户输入处理、API 调用等。从主页面组件中分离出来，
 * 以支持 Next.js 的服务器端渲染和静态生成。
 * 
 * 功能特性：
 * - 需求提示词输入和防抖处理
 * - 实时案例生成
 * - 案例选择和展示
 * - 需求文档生成和预览
 * - 错误处理和加载状态
 */

'use client';

import { useState } from 'react';
import PromptInput from '@/components/PromptInput';
import CaseCard from '@/components/CaseCard';
import DocumentPreview from '@/components/DocumentPreview';
import { generateCases, generateDemand } from '@/lib/api';
import { Case } from '@/lib/types';
import { useTranslation } from '@/lib/i18n';
import { Button } from '@/components/ui/Button';

import { Card, CardContent } from '@/components/ui/Card';

/**
 * 主页面客户端组件属性接口
 */
interface HomeClientProps {
  /** 当前语言环境 */
  locale: string;
}

/**
 * 主页面客户端组件
 * 
 * 处理所有需要客户端交互的功能，包括用户输入、状态管理和 API 调用。
 * 使用防抖技术优化用户输入体验，避免频繁的 API 调用。
 * 
 * @param props - 组件属性
 * @returns React 函数组件
 */
export default function HomeClient({ locale }: HomeClientProps) {
  const { t } = useTranslation(locale);
  
  // 状态管理
  const [prompt, setPrompt] = useState<string>('');                    // 用户输入的提示词
  const [cases, setCases] = useState<Case[]>([]);                     // 生成的案例列表
  const [selectedCase, setSelectedCase] = useState<number | null>(null); // 用户选中的案例索引
  const [documentUrl, setDocumentUrl] = useState<string | null>(null); // 生成的文档 URL
  const [loading, setLoading] = useState<boolean>(false);             // 加载状态
  const [error, setError] = useState<string | null>(null);            // 错误信息

  /**
   * 手动生成案例
   *
   * 当用户点击"生成案例"按钮时，调用 API 生成相关的需求案例。
   * 只有在用户主动触发时才会发送请求。
   */
  const handleGenerateCases = async () => {
    if (!prompt.trim()) {
      setError(t('error.empty_prompt'));
      return;
    }

    setLoading(true);
    setError(null);
    setCases([]); // 清空之前的案例
    setSelectedCase(null); // 重置选中状态
    setDocumentUrl(null); // 重置文档

    try {
      const data = await generateCases({ prompt: prompt.trim(), locale });
      setCases(data.cases);
      setLoading(false);
    } catch {
      setError(t('error.api'));
      setLoading(false);
    }
  };

  /**
   * 处理案例选择
   * 
   * 当用户点击选择某个案例时，更新选中状态并重置文档 URL。
   * 这确保了用户在选择新案例时不会看到之前案例的文档。
   * 
   * @param caseId - 被选中案例的索引
   */
  const handleSelectCase = (caseId: number) => {
    setSelectedCase(caseId);
    setDocumentUrl(null); // 重置需求单，确保不显示之前的文档
  };

  /**
   * 生成需求文档
   * 
   * 根据用户选择的案例，调用后端 API 生成详细的需求文档。
   * 包含完整的错误处理和加载状态管理。
   */
  const handleGenerateDocument = async () => {
    // 确保有选中的案例
    if (selectedCase === null) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const data = await generateDemand({ case_id: selectedCase, locale });
      setDocumentUrl(data.documentUrl);
      setLoading(false);
    } catch {
      setError(t('error.document'));
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <div className="container mx-auto px-4 py-12">
        {/* 英雄区域 */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl mb-8 shadow-lg">
            <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
          <h1 className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent mb-6">
            {t('title')}
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed mb-8">
            {t('subtitle')}
          </p>
          <div className="flex justify-center">
            <div className="flex items-center space-x-8 text-sm text-gray-500">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-2 animate-pulse"></div>
                AI驱动
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse"></div>
                智能分析
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-purple-500 rounded-full mr-2 animate-pulse"></div>
                快速生成
              </div>
            </div>
          </div>
        </div>

        {/* 主要内容区域 */}
        <div className="max-w-4xl mx-auto">
          {/* 提示词输入卡片 */}
          <Card className="mb-12 shadow-xl border-0 bg-white/80 backdrop-blur-sm">
            <CardContent className="p-10">
              <div className="text-center mb-8">
                <h2 className="text-2xl font-semibold text-gray-800 mb-3">
                  描述您的项目需求
                </h2>
                <p className="text-gray-600">
                  简单描述您想要开发的项目，AI将为您生成详细的案例方案
                </p>
              </div>

              <PromptInput
                prompt={prompt}
                onChange={(value) => setPrompt(value)}
                placeholder={t('prompt.placeholder')}
                disabled={loading}
              />

              {/* 生成案例按钮 */}
              <div className="flex justify-center mt-8">
                <Button
                  variant="primary"
                  size="lg"
                  onClick={handleGenerateCases}
                  disabled={loading || !prompt.trim()}
                  loading={loading}
                  className="px-12 py-4 text-lg font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                  {t('generate_cases')}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* 错误提示区域 */}
          {error && (
            <Card className="mb-8 border-red-200 bg-red-50">
              <CardContent className="p-6">
                <div className="flex items-center text-red-700">
                  <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                  {error}
                </div>
              </CardContent>
            </Card>
          )}

          {/* 加载状态 */}
          {loading && (
            <Card className="mb-12 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
              <CardContent className="p-10">
                <div className="text-center">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-full mb-6">
                    <div className="w-8 h-8 border-4 border-white border-t-transparent rounded-full animate-spin"></div>
                  </div>
                  <h3 className="text-xl font-semibold text-gray-800 mb-2">
                    AI正在分析您的需求
                  </h3>
                  <p className="text-gray-600">
                    正在生成个性化的项目案例，请稍候...
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* 案例展示区域 */}
          {cases.length > 0 && (
            <div className="mb-12">
              <div className="text-center mb-10">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">
                  🎯 为您推荐的项目案例
                </h2>
                <p className="text-gray-600 text-lg">
                  选择最符合您需求的案例，我们将为您生成详细的需求文档
                </p>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {cases.map((caseItem, idx) => (
                  <CaseCard
                    key={idx}
                    caseItem={caseItem}
                    isSelected={selectedCase === idx}
                    onSelect={() => handleSelectCase(idx)}
                    locale={locale}
                  />
                ))}
              </div>
            </div>
          )}

          {/* 生成需求单按钮 */}
          {selectedCase !== null && (
            <div className="text-center mb-12">
              <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-8 border border-green-200">
                <div className="mb-6">
                  <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-green-500 to-blue-500 rounded-full mb-4">
                    <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-800 mb-2">
                    准备生成需求文档
                  </h3>
                  <p className="text-gray-600">
                    基于您选择的案例，我们将生成详细的项目需求文档
                  </p>
                </div>
                <Button
                  variant="primary"
                  size="lg"
                  onClick={handleGenerateDocument}
                  disabled={loading}
                  loading={loading}
                  className="px-10 py-4 text-lg font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
                >
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  {t('generate_document')}
                </Button>
              </div>
            </div>
          )}

          {/* 需求单预览和下载区域 */}
          {documentUrl && (
            <Card className="shadow-xl border-0 bg-gradient-to-br from-green-50 to-blue-50">
              <CardContent className="p-10">
                <div className="text-center mb-8">
                  <div className="inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r from-green-500 to-blue-500 rounded-2xl mb-6 shadow-lg">
                    <svg className="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                  </div>
                  <h3 className="text-3xl font-bold text-gray-800 mb-4">
                    🎉 需求文档生成完成！
                  </h3>
                  <p className="text-lg text-gray-600 mb-8">
                    您的项目需求文档已经准备就绪，可以预览和下载
                  </p>
                </div>
                <DocumentPreview documentUrl={documentUrl} locale={locale} />
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
