import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { useDebounce } from 'use-debounce';
import PromptInput from '@/components/PromptInput';
import CaseCard from '@/components/CaseCard';
import DocumentPreview from '@/components/DocumentPreview';
import { generateCases, generateDemand } from '@/lib/api';
import { Case } from '@/lib/types';
import { useTranslation } from '@/lib/i18n';

export default function Home() {
  const router = useRouter();
  const { t } = useTranslation(router.locale || 'en');
  const [prompt, setPrompt] = useState<string>('');
  const [debouncedPrompt] = useDebounce(prompt, 500);
  const [cases, setCases] = useState<Case[]>([]);
  const [selectedCase, setSelectedCase] = useState<number | null>(null);
  const [documentUrl, setDocumentUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // 实时生成案例
  useEffect(() => {
    if (debouncedPrompt) {
      setLoading(true);
      setError(null);
      generateCases({ prompt: debouncedPrompt, locale: router.locale || 'en' })
        .then((data) => {
          setCases(data.cases);
          setLoading(false);
        })
        .catch((err) => {
          setError(t('error.api'));
          setLoading(false);
        });
    }
  }, [debouncedPrompt, router.locale, t]);

  // 处理案例选择
  const handleSelectCase = (caseId: number) => {
    setSelectedCase(caseId);
    setDocumentUrl(null); // 重置需求单
  };

  // 生成需求单
  const handleGenerateDocument = async () => {
    if (selectedCase === null) return;
    setLoading(true);
    setError(null);
    try {
      const data = await generateDemand({ case_id: selectedCase, locale: router.locale || 'en' });
      setDocumentUrl(data.documentUrl);
      setLoading(false);
    } catch (err) {
      setError(t('error.document'));
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto p-4 min-h-screen bg-gray-100">
      <h1 className="text-3xl font-bold mb-6 text-center">{t('title')}</h1>

      {/* 提示词输入 */}
      <PromptInput
        prompt={prompt}
        onChange={(value) => setPrompt(value)}
        placeholder={t('prompt.placeholder')}
        disabled={loading}
      />

      {/* 生成案例按钮 */}
      <button
        className="mt-4 bg-blue-500 text-white p-3 rounded-lg hover:bg-blue-600 disabled:bg-blue-300 w-full md:w-auto"
        onClick={() => generateCases({ prompt, locale: router.locale || 'en' })}
        disabled={loading || !prompt}
      >
        {loading ? t('loading') : t('generate_cases')}
      </button>

      {/* 错误提示 */}
      {error && (
        <div className="mt-4 p-3 bg-red-100 text-red-700 rounded">
          {error}
        </div>
      )}

      {/* 案例展示 */}
      <div className="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {cases.map((caseItem, idx) => (
          <CaseCard
            key={idx}
            caseItem={caseItem}
            isSelected={selectedCase === idx}
            onSelect={() => handleSelectCase(idx)}
            locale={router.locale || 'en'}
          />
        ))}
      </div>

      {/* 生成需求单按钮 */}
      {selectedCase !== null && (
        <button
          className="mt-6 bg-purple-500 text-white p-3 rounded-lg hover:bg-purple-600 disabled:bg-purple-300 w-full md:w-auto"
          onClick={handleGenerateDocument}
          disabled={loading}
        >
          {loading ? t('loading') : t('generate_document')}
        </button>
      )}

      {/* 需求单预览和下载 */}
      {documentUrl && (
        <DocumentPreview documentUrl={documentUrl} locale={router.locale || 'en'} />
      )}
    </div>
  );
}