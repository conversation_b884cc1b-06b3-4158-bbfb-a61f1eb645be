/**
 * API 客户端库
 *
 * 该文件提供与后端 API 服务通信的客户端函数。
 * 包含案例生成和需求文档生成的 API 调用功能，
 * 以及相关的 TypeScript 类型定义。
 *
 * 功能特性：
 * - 类型安全的 API 调用
 * - 错误处理机制
 * - 支持国际化参数
 * - RESTful API 接口封装
 */

import { Case } from './types';

/**
 * 生成案例请求参数接口
 * 定义调用案例生成 API 时需要的参数
 */
export interface GenerateCasesRequest {
  /** 用户输入的需求提示词 */
  prompt: string;
  /** 目标语言代码 */
  locale: string;
}

/**
 * 生成需求文档请求参数接口
 * 定义调用需求文档生成 API 时需要的参数
 */
export interface GenerateDemandRequest {
  /** 选中的案例 ID */
  case_id: number;
  /** 目标语言代码 */
  locale: string;
}

/**
 * 生成需求案例
 *
 * 根据用户输入的提示词，调用后端 AI 服务生成多个相关的需求案例。
 * 返回的案例包含标题、描述和详细要求列表。
 *
 * @param request - 包含提示词和语言设置的请求参数
 * @returns Promise，解析为包含案例数组的响应对象
 * @throws 当 API 调用失败时抛出错误
 */
export async function generateCases(request: GenerateCasesRequest): Promise<{ cases: Case[] }> {
  const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/generate-cases`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(request),
  });

  // 检查响应状态，如果失败则抛出错误
  if (!response.ok) {
    throw new Error('Failed to generate cases');
  }

  return response.json();
}

/**
 * 生成需求文档
 *
 * 根据用户选择的案例，调用后端服务生成详细的需求文档。
 * 返回生成文档的 URL 地址，用户可以预览和下载。
 *
 * @param request - 包含案例 ID 和语言设置的请求参数
 * @returns Promise，解析为包含文档 URL 的响应对象
 * @throws 当 API 调用失败时抛出错误
 */
export async function generateDemand(request: GenerateDemandRequest): Promise<{ documentUrl: string }> {
  const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL}/generate-demand`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(request),
  });

  // 检查响应状态，如果失败则抛出错误
  if (!response.ok) {
    throw new Error('Failed to generate demand document');
  }

  return response.json();
}