from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from .ai_service import generate_cases_from_prompt
from typing import List

router = APIRouter()

class PromptRequest(BaseModel):
    prompt: str
    locale: str = "en"

class CaseResponse(BaseModel):
    id: int
    title: str
    description: str
    details: List[str]

@router.post("/generate-cases", response_model=dict)
async def generate_cases(request: PromptRequest):
    try:
        cases = await generate_cases_from_prompt(request.prompt, request.locale)
        return {"cases": cases}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate cases: {str(e)}")