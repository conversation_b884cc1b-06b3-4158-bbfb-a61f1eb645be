"""
AI服务模块

该模块负责与外部AI服务（如Grok 3）进行交互，
提供案例生成和需求文档生成功能。
"""

import httpx
import os
import json
from typing import List, Dict, Any
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def generate_cases_from_prompt(prompt: str, locale: str) -> List[Dict[str, Any]]:
    """
    根据用户提示词生成需求案例

    Args:
        prompt: 用户输入的需求描述
        locale: 目标语言（'en' 或 'zh'）

    Returns:
        包含案例信息的字典列表
    """
    try:
        # 如果有API密钥，调用真实的AI服务
        api_key = os.getenv('XAI_API_KEY')
        if api_key and api_key != 'your_xai_api_key_here':
            return await _call_ai_service(prompt, locale, "cases")
        else:
            # 使用模拟数据进行开发测试
            logger.warning("使用模拟数据，请配置XAI_API_KEY以使用真实AI服务")
            return _generate_mock_cases(prompt, locale)
    except Exception as e:
        logger.error(f"生成案例失败: {str(e)}")
        # 降级到模拟数据
        return _generate_mock_cases(prompt, locale)

async def generate_demand_document(case_id: int, locale: str) -> str:
    """
    根据案例ID生成需求文档

    Args:
        case_id: 选中的案例ID
        locale: 目标语言（'en' 或 'zh'）

    Returns:
        Markdown格式的需求文档内容
    """
    try:
        # 如果有API密钥，调用真实的AI服务
        api_key = os.getenv('XAI_API_KEY')
        if api_key and api_key != 'your_xai_api_key_here':
            return await _call_ai_service(f"case_{case_id}", locale, "document")
        else:
            # 使用模拟数据进行开发测试
            logger.warning("使用模拟数据，请配置XAI_API_KEY以使用真实AI服务")
            return _generate_mock_document(case_id, locale)
    except Exception as e:
        logger.error(f"生成文档失败: {str(e)}")
        # 降级到模拟数据
        return _generate_mock_document(case_id, locale)

async def _call_ai_service(prompt: str, locale: str, service_type: str) -> Any:
    """
    调用外部AI服务

    Args:
        prompt: 输入提示词
        locale: 目标语言
        service_type: 服务类型（'cases' 或 'document'）

    Returns:
        AI服务的响应结果
    """
    api_url = os.getenv("XAI_API_URL", "https://api.x.ai/v1/chat/completions")
    api_key = os.getenv("XAI_API_KEY")

    if service_type == "cases":
        system_prompt = f"""你是一个专业的需求分析师。根据用户的需求描述，生成3个具体的项目案例。
        每个案例需要包含：
        1. 标题（简洁明了）
        2. 描述（一句话概括）
        3. 详细需求列表（3-5个具体功能点）

        请用{locale}语言回复，返回JSON格式：
        [
          {{
            "id": 0,
            "title": "案例标题",
            "description": "案例描述",
            "details": ["需求1", "需求2", "需求3", "需求4", "需求5"]
          }}
        ]"""
        user_prompt = f"用户需求：{prompt}"
    else:
        system_prompt = f"""你是一个专业的技术文档编写专家。根据项目案例，生成详细的需求文档。
        文档需要包含以下部分：
        1. 项目背景
        2. 项目目标
        3. 功能需求
        4. 技术要求
        5. 预算范围
        6. 时间计划

        请用{locale}语言回复，使用Markdown格式。"""
        user_prompt = f"项目案例ID：{prompt}"

    async with httpx.AsyncClient(timeout=30.0) as client:
        response = await client.post(
            api_url,
            json={
                "model": "grok-beta",
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": user_prompt}
                ],
                "temperature": 0.7,
                "max_tokens": 2000
            },
            headers={
                "Authorization": f"Bearer {api_key}",
                "Content-Type": "application/json"
            }
        )
        response.raise_for_status()

        result = response.json()
        content = result["choices"][0]["message"]["content"]

        if service_type == "cases":
            # 尝试解析JSON响应
            try:
                return json.loads(content)
            except json.JSONDecodeError:
                logger.warning("AI返回的不是有效JSON，使用模拟数据")
                return _generate_mock_cases(prompt, locale)
        else:
            return content

def _generate_mock_cases(prompt: str, locale: str) -> List[Dict[str, Any]]:
    """生成模拟案例数据"""
    if locale == "zh":
        base_cases = [
            {
                "id": 0,
                "title": "电子商务网站",
                "description": "功能完整的在线购物平台",
                "details": ["用户注册登录", "商品展示目录", "购物车功能", "在线支付", "订单管理系统"]
            },
            {
                "id": 1,
                "title": "企业官网",
                "description": "专业的企业形象展示网站",
                "details": ["公司介绍页面", "产品服务展示", "新闻资讯模块", "联系我们页面", "响应式设计"]
            },
            {
                "id": 2,
                "title": "内容管理系统",
                "description": "灵活的内容发布和管理平台",
                "details": ["文章发布系统", "用户权限管理", "评论互动功能", "搜索功能", "数据统计分析"]
            }
        ]
    else:
        base_cases = [
            {
                "id": 0,
                "title": "E-commerce Website",
                "description": "Full-featured online shopping platform",
                "details": ["User registration", "Product catalog", "Shopping cart", "Payment integration", "Order management"]
            },
            {
                "id": 1,
                "title": "Corporate Website",
                "description": "Professional business showcase website",
                "details": ["Company introduction", "Product showcase", "News section", "Contact page", "Responsive design"]
            },
            {
                "id": 2,
                "title": "Content Management System",
                "description": "Flexible content publishing platform",
                "details": ["Article publishing", "User management", "Comment system", "Search functionality", "Analytics dashboard"]
            }
        ]

    # 根据用户输入调整案例内容
    if "移动" in prompt or "mobile" in prompt.lower():
        base_cases[0]["title"] = "移动应用" if locale == "zh" else "Mobile App"
        base_cases[0]["description"] = "跨平台移动应用程序" if locale == "zh" else "Cross-platform mobile application"

    return base_cases

def _generate_mock_document(case_id: int, locale: str) -> str:
    """生成模拟需求文档"""
    if locale == "zh":
        return f"""# 项目需求文档

## 项目背景
基于用户选择的案例 #{case_id}，我们需要开发一个现代化的数字解决方案。该项目旨在满足当前市场需求，提供优质的用户体验。

## 项目目标
- 构建用户友好的界面
- 实现核心业务功能
- 确保系统稳定性和安全性
- 提供良好的性能表现

## 功能需求

### 核心功能
1. **用户管理系统**
   - 用户注册和登录
   - 个人信息管理
   - 权限控制

2. **主要业务功能**
   - 数据展示和管理
   - 交互操作界面
   - 实时更新机制

3. **系统管理**
   - 后台管理界面
   - 数据统计分析
   - 系统监控

## 技术要求
- **前端**: React/Vue.js + TypeScript
- **后端**: Node.js/Python + 数据库
- **部署**: 云服务器 + CDN
- **安全**: HTTPS + 数据加密

## 预算范围
- 开发成本: 5-15万元
- 运维成本: 每月1000-3000元
- 第三方服务: 根据实际使用量

## 时间计划
- **需求分析**: 1-2周
- **设计阶段**: 2-3周
- **开发阶段**: 6-10周
- **测试部署**: 2-3周
- **总计**: 3-4个月

## 交付物
- 完整的源代码
- 部署文档
- 用户使用手册
- 技术维护文档

---
*本文档由AI需求生成器自动生成，请根据实际情况进行调整。*"""
    else:
        return f"""# Project Requirements Document

## Project Background
Based on the selected case #{case_id}, we need to develop a modern digital solution. This project aims to meet current market demands and provide excellent user experience.

## Project Objectives
- Build user-friendly interface
- Implement core business functions
- Ensure system stability and security
- Provide good performance

## Functional Requirements

### Core Features
1. **User Management System**
   - User registration and login
   - Profile management
   - Permission control

2. **Main Business Functions**
   - Data display and management
   - Interactive operation interface
   - Real-time update mechanism

3. **System Administration**
   - Admin dashboard
   - Data analytics
   - System monitoring

## Technical Requirements
- **Frontend**: React/Vue.js + TypeScript
- **Backend**: Node.js/Python + Database
- **Deployment**: Cloud server + CDN
- **Security**: HTTPS + Data encryption

## Budget Range
- Development cost: $7,000 - $20,000
- Operation cost: $150 - $400/month
- Third-party services: Based on usage

## Timeline
- **Requirements Analysis**: 1-2 weeks
- **Design Phase**: 2-3 weeks
- **Development Phase**: 6-10 weeks
- **Testing & Deployment**: 2-3 weeks
- **Total**: 3-4 months

## Deliverables
- Complete source code
- Deployment documentation
- User manual
- Technical maintenance guide

---
*This document is automatically generated by AI Demand Generator. Please adjust according to actual requirements.*"""