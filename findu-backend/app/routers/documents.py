from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from .pdf_service import generate_pdf_document
from .ai_service import generate_demand_document

router = APIRouter()

class CaseSelection(BaseModel):
    case_id: int
    locale: str = "en"

@router.post("/generate-demand", response_model=dict)
async def generate_demand(request: CaseSelection):
    try:
        content = await generate_demand_document(request.case_id, request.locale)
        document_url = generate_pdf_document(content, request.locale)
        return {"documentUrl": document_url}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate document: {str(e)}")