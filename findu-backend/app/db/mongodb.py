from pymongo import MongoClient
import os

class MongoDB:
    def __init__(self):
        self.client = MongoClient(os.getenv("MONGODB_URL", "mongodb://localhost:27017"))
        self.db = self.client["demand_ai"]

    def save_prompt(self, user_id: str, prompt: str, locale: str):
        collection = self.db["prompts"]
        collection.insert_one({"user_id": user_id, "prompt": prompt, "locale": locale, "created_at": os.times().elapsed})

    def save_case(self, prompt_id: str, case_data: dict):
        collection = self.db["cases"]
        collection.insert_one({"prompt_id": prompt_id, **case_data, "created_at": os.times().elapsed})

mongo_db = MongoDB()