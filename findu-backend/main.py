from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv
import os
from app.routers import cases, documents
from app.utils.auth import init_jwt

# 加载环境变量
load_dotenv()

app = FastAPI(title="AI Demand Generator API")

# 配置CORS，允许前端跨域请求
app.add_middleware(
    CORSMiddleware,
    allow_origins=[os.getenv("FRONTEND_URL", "http://localhost:3000")],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 初始化JWT
init_jwt(os.getenv("JWT_SECRET", "your_jwt_secret"))

# 注册路由
app.include_router(cases.router, prefix="/api")
app.include_router(documents.router, prefix="/api")

@app.get("/health")
async def health_check():
    return {"status": "healthy"}